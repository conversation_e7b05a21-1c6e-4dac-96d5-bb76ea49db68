<!DOCTYPE html>
<html lang="en">

<head>
    <title><?php echo $this->lang->line('savsoft_quiz'); ?></title>
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0"> -->
    <meta charset="utf-8" name="viewport" content="width=device-width, user-scalable=no" />
    <link rel="stylesheet" href="<?php echo base_url(); ?>public/css/app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'>
    <link rel="stylesheet" href="<?php echo base_url(); ?>css/plugins/HoldOn.min.css">

    <!-- <link rel="stylesheet" type="text/css" href="https://www.exam.ptfinalexam.com/assets/backend/css/custom.css"> -->
    <script src="<?php echo base_url(); ?>js/jquery.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/jcider.min.js"></script>
    <script src="<?php echo base_url(); ?>public/js/app.js"></script>
    <script src="<?php echo base_url(); ?>js/plugins/HoldOn.min.js"></script>
    <script src="https://cdn.amplitude.com/libs/analytics-browser-2.11.1-min.js.gz"></script>
    <script src="https://cdn.amplitude.com/libs/plugin-session-replay-browser-1.6.22-min.js.gz"></script>
    <script>
        window.amplitude.add(window.sessionReplay.plugin({
            sampleRate: 1
        }));
        window.amplitude.init('9a4d2da56616c26ad4ed4e41eeb81079', {
            "autocapture": {
                "elementInteractions": true
            }
        });
    </script>
    <script>
        BASE_URL = "<?php echo base_url(); ?>";
    </script>
    <noscript>
        <div class="noscript-page text-center">
            <div class="error-content">
                <h3>Your browser does not support JavaScript!</h3>
            </div>
        </div>
    </noscript>
    <style>
        :root {
            --color-green-primary: #12705B;
            --color-green-400: #31C48D;
            --color-green-500: #0E9F6E;
            --color-green-200: #BCF0DA;
            --color-green-700: #046C4E;
            --color-green-50: #F3FAF7;
        }

        .next-btn.active {
            background-color: var(--color-green-primary) !important;
        }

        .cert-item.selected {
            border-color: var(--color-green-primary) !important;
        }

        .cert-item.selected::before {
            background: url(/images/itcertprep/selected.png) no-repeat top right;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            color: var(--color-green-primary) !important;
            border-left: 1px solid #D1D5DB;
            /* border-right: 1px solid #D1D5DB; */
            background-color: var(--color-green-100) !important;

        }

        .dataTables_wrapper .dataTables_filter input:focus {
            box-shadow: none;
            border-color: var(--color-green-400);
        }

        .datepicker-controls button:disabled {
            background-color: #fff !important;
        }

        #dropdownRadioButton.active,
        #dropdownRadioButton2.active,
        .bg-primary-blue-1 {
            background-color: var(--color-green-primary) !important;
        }

        .focus\:ring-blue-200:focus {
            --tw-ring-color: var(--color-green-200) !important;
        }

        .hover\:bg-\[\#EBF5FF\]:hover .bg-primary-blue-1,
        .bg-blue-700 {
            background-color: #12705B !important;
        }

        #submit_button.saveButton:hover {
            background-color: var(--color-green-400) !important;
            outline-color: var(--color-green-200);
        }

        .hover\:bg-\[\#EBF5FF\]:hover {
            background-color: var(--color-green-50) !important;
        }

        .text-primary-blue-1 {
            color: #12705B !important;
        }

        .hover\:bg-blue-400:hover {
            background-color: var(--color-green-400) !important;
        }

        .bg-blue-600,
        .bg-blue-500 {
            background-color: var(--color-green-primary) !important;
        }

        .focus\:ring-blue-300:focus {
            --tw-ring-color: transparent;
        }

        .hover\:ring-blue-300:hover {
            --tw-ring-color: var(--color-green-200) !important;
        }

        .focus\:border-blue-500:focus {
            border-color: #12705B;
        }

        .peer:checked~.peer-checked\:bg-blue-600 {
            background-color: #12705B;
        }

        [type='text']:focus,
        [type='email']:focus,
        [type='password']:focus,
        [type='number']:focus,
        select:focus {
            --tw-ring-color: #12705B;
            border-color: #12705B;
        }

        .basic_container .num_item.selected .num_text {
            color: #12705B;
        }

        .basic_container .num_item.selected input {
            border-color: #12705B;
        }

        .advance_container .domain_item.selected {
            border-color: #12705B;
            color: #12705B;
            background-color: #EBF5FF;
        }

        .text-blue-600,
        .hover\:text-blue-600:hover {
            color: #12705B;
        }

        .navigator-section .next-btn,
        .navigator-section .back-btn,
        .navigator-section .result-btn {
            color: #12705B;
            border-color: #12705B;
        }

        .navigator-section .next-btn img,
        .navigator-section .back-btn img,
        .navigator-section .result-btn img,
        .previous-question img,
        .next-question img {
            filter: brightness(0) saturate(100%) invert(49%) sepia(36%) saturate(926%) hue-rotate(153deg) brightness(94%) contrast(100%);
        }

        [type='radio'] {
            color: #12705B;
        }

        .border-blue-600,
        .border-primary-blue-1 {
            border-color: #12705B;
        }

        .mark-active-indicator,
        .active-indicator {
            background-color: #12705B !important;
        }

        .text-\[\#4776ED\] {
            color: var(--color-green-primary) !important;
        }

        .text-\[\#4776ED\]:hover {
            color: var(--color-green-400) !important;
        }

        .text-\[\#4776ED\]:focus {
            color: var(--color-green-700) !important;
        }

        [type='checkbox']:focus,
        [type='radio']:focus {
            --tw-ring-color: var(--color-green-400) !important;
            border-color: var(--color-green-400) !important;
        }

        .custom-input:checked {
            background-color: var(--color-green-primary) !important;
        }

        .showNoti {
            opacity: 0;
            transition: 0.5s opacity;
            display: flex;
        }

        /* Polar Payment Popup Styles */
        #polarPaymentPopup {
            transition: opacity 0.3s ease-in-out;
        }

        #polarPaymentPopup>div {
            transition: height 0.3s ease-in-out;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        #closePolarPopup {
            transition: all 0.2s;
        }

        #closePolarPopup:hover {
            background-color: #f3f4f6;
            transform: scale(1.05);
        }

        li.step-done::after {
            background: #1C64F2;
        }
        body {
             background: linear-gradient(to bottom, #FFFFFF, #E7FCFA);
        }

        .final-step-bg {
            background: none;
        }
    </style>
</head>

<body class="w-screen h-screen relative overflow-x-hidden">
    <div id="error_message" class="showNoti z-[100] absolute flex items-start top-[25px] right-[25px] w-[380px] bg-[#FE4D01] px-[16px] py-[12px] rounded-[4px]">
        <img class="mr-[10px]" src="<?php echo base_url(); ?>images/icons/Close-icon.svg">
        <p id="error_text" class="text-[14px] text-white w-[290px]"></p>
        <img onclick="closeNotification()" src="<?php echo base_url(); ?>images/icons/x.svg" class="ml-[12px] cursor-pointer">
    </div>

    <div id="success_message" class="showNoti z-50 absolute flex items-start top-[25px] right-[25px] w-[380px] bg-[#0066D3] px-[16px] py-[12px] rounded-[4px]">
        <img class="mr-[10px]" src="<?php echo base_url(); ?>images/icons/info-circle.svg">
        <p id="success_text" class="text-[14px] text-white w-[290px]"></p>
        <img onclick="closeNotification()" src="<?php echo base_url(); ?>images/icons/x.svg" class="ml-[12px] cursor-pointer">
    </div>
    <!-- Header -->
    <div class="container ml-auto mr-auto w-full flex items-center p-4">
        <div class="w-3/12">
            <div class="logo">
                <a href="<?php echo base_url(); ?>">
                    <img src="<?php echo base_url(); ?>images/new/itcertprep/logo_with_text.svg" width="160" alt="logo">
                </a>
            </div>
        </div>
        <div class="w-6/12">

        </div>
        <div class="w-3/12 flex justify-end">
            <a id="help-button" class="flex cursor-pointer" href="https://m.me/scrumpassvn" target="_blank">
                <img src="<?php echo base_url(); ?>images/itcertprep/Help.svg" alt="Help">
                <span class="text-sm font-medium text-primary-blue-1 ml-1">Support</span>
            </a>
        </div>
    </div>
    <!-- End Header -->
    <div class="main-content ">
        <!-- Mobile Progress bar -->
        <div class="w-full lg:hidden h-14 progress-bar mb-6">
        </div>
        <!-- Main Content -->
        <!-- Step 4 -->
        <div class="step-4 w-screen h-screen hidden final-step-bg main-final-step-content hidden">
            <div class="text-center">
                <div class="p-4">
                    <h2 class="text-xl font-bold text-gray-900 mb-2">You're all set!</h2>
                    <div class="step-4-content">
                        <p class="text-gray-500 text-sm font-medium">
                            Your registration for the <span class="cert-title font-bold"></span> is complete.
                        </p>
                        <p class="text-gray-500 text-sm font-medium">
                            This should take no more than 15 minutes.
                        </p>
                        <p class="text-gray-500 text-sm font-medium">
                            Can't find the email? Check your Spam/Promotions folder or contact us at <a href="mailto:<EMAIL>" class="text-primary-blue-1"><EMAIL></a>
                        </p>
                    </div>


                </div>
                <!-- Image -->
                <div class="lg:mb-12 mb-10 logo-last-step">
                    <img src="<?= base_url() ?>/images/itcertprep/relax.png" alt="Success Illustration" class="w-full hidden lg:block mt-20" />
                    <img src="<?= base_url() ?>/images/itcertprep/relax.png" alt="Success Illustration" class="w-full center-block lg:hidden" />
                </div>

                <!-- Buttons -->
                <div class="flex flex-col lg:flex-row items-center justify-center gap-3 lg:gap-4 mb-6 lg:mb-0 step-4-buttons">
                    <a href="https://exam.itcertprep.com/userDashboard" target="_blank" class="px-5 py-3 h-12 text-base font-medium bg-primary-blue-1 text-white rounded-lg hover:bg-blue-400 transition-colors text-center" id="join-group-btn">
                        Start learning
                    </a>
                    <a href="https://itcertprep.com" target="_blank" class="px-5 py-3 h-12 border border-primary-blue-1 text-primary-blue-1 rounded-lg hover:bg-blue-100 transition-colors text-base font-medium text-center" id="go-to-homepage-btn">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
        <!-- End Step 4 -->
        <!-- Mobile Footer -->
        <div class="fixed bottom-0 left-0 right-0 bg-white border-t p-4 hidden lg:hidden mobile-footer">
            <div class="flex justify-between gap-4">
                <button class="back-btn px-6 py-2 border border-gray-200 rounded-lg text-gray-800 hover:bg-gray-50 flex-1">
                    Quay lại
                </button>
                <button class="next-btn px-6 py-3 bg-primary-blue-1 text-white rounded-lg hover:bg-blue-700 flex-1">
                    Tiếp tục
                </button>
            </div>
        </div>
</body>
<script>
    $(document).ready(function() {
        const certTitle = localStorage.getItem('certTitle');
        $('.cert-title').text(certTitle);
        const email = localStorage.getItem('user_email');
        const cert = localStorage.getItem('selectedCertId');
        const plan = localStorage.getItem('selectedPlan');
        const formData = JSON.parse(localStorage.getItem('formData')) || {};
        const urlParams = new URLSearchParams(window.location.search);
        const transId = urlParams.get('id');
        $.ajax({
            url: BASE_URL + 'apiSignUp/process_purchase',
            type: 'POST',
            data: {
                email: email,
                cert: cert,
                plan: plan,
                name: formData.name,
                know_from: formData.know_from,
                exam_date: formData.exam_date,
                trans_id: transId,
            },
            beforeSend: function() {
                HoldOn.open({
                    theme: "sk-cube-grid",
                });
            },
            success: function(data) {
                HoldOn.close();
                $('.step-4').removeClass('hidden');
                localStorage.removeItem('selectedBank');
				localStorage.removeItem('focusedInput');
				localStorage.removeItem('selectedCertId');
				localStorage.removeItem('selectedPlan');
				localStorage.removeItem('formData');
				localStorage.removeItem('selectedPayment');
				localStorage.removeItem('currentStep');
				localStorage.removeItem('uid');
				localStorage.removeItem('user_name');
				localStorage.removeItem('user_id');
				localStorage.removeItem('user_email');
            }
        });
    });
</script>

<!-- Login Popup -->
<div id="loginModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg w-[416px] shadow-lg relative">
        <!-- Header -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <div class="flex items-center">
                <h3 class="text-xl font-bold text-gray-900">Login</h3>
            </div>
            <button id="closeLoginModal" class="focus:outline-none">
                <img src="<?= base_url() ?>/images/icons/close_icon.svg" alt="Close" class="w-3 h-3">
            </button>
        </div>

        <!-- Body -->
        <div class="p-8">
            <form id="loginForm" class="space-y-6">
                <!-- Email Field -->
                <div>
                    <label for="login-email" class="block text-sm font-medium text-gray-900 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" id="login-email" name="login-email" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-2 focus:border-green-500">
                    </div>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="login-password" class="block text-sm font-medium text-gray-900 mb-2">Password</label>
                    <div class="relative">
                        <input type="password" id="login-password" name="password" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-2 focus:border-green-500">
                        <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <img src="<?= base_url() ?>/images/icons/show_password_icon.svg" alt="Show Password" class="w-3 h-3">
                        </button>
                    </div>
                    <div class="text-right mt-2">
                        <a href="#" id="forgotPasswordLink" class="text-sm font-medium text-green-600 hover:text-green-500">Forgot password?</a>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex gap-4">
                    <button type="submit" id="loginConfirmBtn" class="bg-primary-blue-1 text-white py-2.5 px-5 rounded-lg font-medium transition-colors">
                        Confirm
                    </button>
                    <button type="button" id="loginCancelBtn" class="border border-gray-200 text-gray-900 py-2.5 px-5 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Forgot Password Popup -->
<div id="forgotPasswordModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg w-[416px] shadow-lg relative">
        <!-- Header -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
            <div class="flex items-center">
                <button id="backToLoginBtn" class="mr-2">
                    <img src="<?= base_url() ?>/images/icons/back_arrow.svg" alt="Back" class="w-5 h-5">
                </button>
                <h3 class="text-xl font-bold text-gray-900">Forgot password</h3>
            </div>
            <button id="closeForgotPasswordModal" class="focus:outline-none">
                <img src="<?= base_url() ?>/images/icons/close_icon.svg" alt="Close" class="w-3 h-3">
            </button>
        </div>

        <!-- Body -->
        <div class="p-8">
            <form id="forgotPasswordForm" class="space-y-6">
                <!-- Email Field -->
                <div>
                    <label for="forgot-email" class="block text-sm font-medium text-gray-900 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" id="forgot-email" name="email" required
                            class="w-full p-2 bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm text-gray-900 focus:ring-2 focus:border-green-500">
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex gap-4">
                    <button type="submit" id="forgotConfirmBtn" class="w-1/2 bg-primary-blue-1 text-white py-2.5 px-5 rounded-lg font-medium  transition-colors">
                        Confirm
                    </button>
                    <button type="button" id="forgotCancelBtn" class="w-1/2 border border-gray-200 text-gray-900 py-2.5 px-5 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

</html>